<template>
  <div class="meeting-page">
    <!-- 搜索框 -->
    <div class="search-container">
      <VanSearch
        v-model="searchKeyword"
        placeholder="输入会议名称查询"
        background="#f5f5f5"
        @search="onSearch"
        @clear="onClear"
      />
    </div>

    <!-- 会议列表 -->
    <div class="meeting-list">
      <div
        v-for="meeting in filteredMeetings"
        :key="meeting.id"
        class="meeting-item"
      >
        <div class="meeting-header">
          <h3 class="meeting-title">{{ meeting.title }}</h3>
        </div>

        <div class="meeting-info">
          <div class="info-row">
            <span class="label">开始时间</span>
            <span class="value">{{ meeting.startTime }}</span>
          </div>
          <div class="info-row">
            <span class="label">会议地点</span>
            <span class="value">{{ meeting.location }}</span>
          </div>
        </div>

        <div class="meeting-agenda">
          <div class="agenda-title">会议议题</div>
          <div class="agenda-content">
            {{ meeting.agenda }}
          </div>
        </div>

        <div class="meeting-actions">
          <VanButton
            type="primary"
            size="large"
            round
            block
            @click="viewDetails(meeting)"
          >
            查看详情
          </VanButton>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredMeetings.length === 0" class="empty-state">
        <VanEmpty description="没有更多了" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const meetings = ref([])

// 模拟会议数据
const mockMeetings = [
  {
    id: 1,
    title: '紧急技术评审',
    startTime: '2025-06-30 18:58:51',
    location: '成都市农业农村局1006会议室',
    agenda: '1、关于高标准农田建设的技术方案汇报；2、关于技术方案的头脑风暴讨论；3、关于建设周期。'
  },
  {
    id: 2,
    title: '紧急技术评审',
    startTime: '2025-06-30 18:58:51',
    location: '成都市农业农村局1006会议室成都市农业农村局1006会议室',
    agenda: '1、关于高标准农田建设的技术方案汇报；2、关于技术方案的头脑风暴讨论；3、关于建设周期。'
  }
]

// 计算属性 - 过滤后的会议列表
const filteredMeetings = computed(() => {
  if (!searchKeyword.value) {
    return meetings.value
  }
  return meetings.value.filter(meeting =>
    meeting.title.includes(searchKeyword.value) ||
    meeting.location.includes(searchKeyword.value) ||
    meeting.agenda.includes(searchKeyword.value)
  )
})

// 方法
const onSearch = () => {
  // 搜索逻辑已通过计算属性实现
  console.log('搜索关键词:', searchKeyword.value)
}

const onClear = () => {
  searchKeyword.value = ''
}

const viewDetails = (meeting) => {
  showToast(`查看会议详情: ${meeting.title}`)
  // 这里可以跳转到详情页面
  // router.push(`/meeting/detail/${meeting.id}`)
}

const fetchMeetings = async () => {
  try {
    // 模拟API请求
    // const response = await window.$http.fetch('/api/meetings')
    // meetings.value = response.data

    // 使用模拟数据
    meetings.value = mockMeetings
  } catch (error) {
    console.error('获取会议列表失败:', error)
    showToast('获取会议列表失败')
  }
}

// 生命周期
onMounted(() => {
  fetchMeetings()
})
</script>

<style lang="scss" scoped>
.meeting-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-container {
  padding: 16px;
  background-color: #fff;
  margin-bottom: 8px;
}

.meeting-list {
  padding: 0 16px;
}

.meeting-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .meeting-header {
    margin-bottom: 12px;
  }

  .meeting-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  .meeting-info {
    margin-bottom: 12px;

    .info-row {
      display: flex;
      margin-bottom: 8px;
      font-size: 14px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #666;
        width: 80px;
        flex-shrink: 0;
      }

      .value {
        color: #333;
        flex: 1;
      }
    }
  }

  .meeting-agenda {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;

    .agenda-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .agenda-content {
      font-size: 14px;
      color: #333;
      line-height: 1.5;
    }
  }

  .meeting-actions {
    margin-top: 16px;
  }
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}
</style>

<route lang="json5">
{
  name: 'Home',
  meta: {
    title: '待开会议'
  }
}
</route>